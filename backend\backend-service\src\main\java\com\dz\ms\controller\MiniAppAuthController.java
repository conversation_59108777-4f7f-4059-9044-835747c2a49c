package com.dz.ms.controller;

import com.dz.ms.common.Result;
import com.dz.ms.dto.MiniAppUserDTO;
import com.dz.ms.service.MiniAppUserService;
import com.dz.ms.vo.MiniAppUserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.groups.Default;

/**
 * 小程序认证控制器
 * <AUTHOR>
 * @date 2025-01-27
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/auth")
@Api(tags = "小程序认证")
@Validated
public class MiniAppAuthController {

    private static final Logger logger = LoggerFactory.getLogger(MiniAppAuthController.class);

    @Autowired
    private MiniAppUserService miniAppUserService;

    @ApiOperation("用户注册")
    @PostMapping("/register")
    public Result<String> register(@Valid @Validated({MiniAppUserDTO.Register.class, Default.class}) @RequestBody MiniAppUserDTO dto) {
        logger.info("用户注册请求，手机号: {}", dto.getPhone());

        // 验证确认密码
        if (!dto.getPassword().equals(dto.getConfirmPassword())) {
            return Result.fail("两次输入的密码不一致");
        }

        // 验证密码长度
        if (dto.getPassword().length() < 6) {
            return Result.fail("密码长度不能少于6位");
        }

        boolean success = miniAppUserService.register(
                dto.getPhone(),
                dto.getPassword(),
                dto.getVerificationCode(),
                dto.getNickname(),
                dto.getAvatar()
        );

        if (success) {
            logger.info("用户注册成功，手机号: {}", dto.getPhone());
            return Result.success("注册成功");
        } else {
            logger.warn("用户注册失败，手机号: {}", dto.getPhone());
            return Result.fail("注册失败，请检查验证码或手机号是否已被注册");
        }
    }

    @ApiOperation("用户登录")
    @PostMapping("/login")
    public Result<String> login(@Valid @Validated({MiniAppUserDTO.Login.class, Default.class}) @RequestBody MiniAppUserDTO dto) {
        logger.info("用户登录请求，手机号: {}", dto.getPhone());

        String token = miniAppUserService.login(dto.getPhone(), dto.getPassword());

        if (token != null) {
            logger.info("用户登录成功，手机号: {}", dto.getPhone());
            return Result.success(token);
        } else {
            logger.warn("用户登录失败，手机号: {}", dto.getPhone());
            return Result.fail("手机号或密码错误");
        }
    }

    @ApiOperation("发送验证码")
    @PostMapping("/sendCode")
    public Result<String> sendVerificationCode(@Valid @Validated({MiniAppUserDTO.SendCode.class, Default.class}) @RequestBody MiniAppUserDTO dto) {
        logger.info("发送验证码请求，手机号: {}", dto.getPhone());

        boolean success = miniAppUserService.sendVerificationCode(dto.getPhone());

        if (success) {
            logger.info("验证码发送成功，手机号: {}", dto.getPhone());
            return Result.success("验证码发送成功");
        } else {
            logger.warn("验证码发送失败，手机号: {}", dto.getPhone());
            return Result.fail("验证码发送失败，请稍后重试");
        }
    }

    @ApiOperation("验证token")
    @PostMapping("/validateToken")
    public Result<Boolean> validateToken(@RequestHeader("Authorization") String authorization) {
        logger.info("验证token请求");

        if (authorization == null || !authorization.startsWith("Bearer ")) {
            return Result.fail("Token格式错误");
        }

        String token = authorization.substring(7);
        boolean valid = miniAppUserService.validateToken(token);

        if (valid) {
            logger.info("Token验证成功");
            return Result.success(true);
        } else {
            logger.warn("Token验证失败");
            return Result.success(false);
        }
    }

    @ApiOperation("获取用户信息")
    @GetMapping("/userInfo")
    public Result<MiniAppUserVO> getUserInfo(@RequestHeader("Authorization") String authorization) {
        logger.info("获取用户信息请求");

        if (authorization == null || !authorization.startsWith("Bearer ")) {
            return Result.fail("Token格式错误");
        }

        String token = authorization.substring(7);
        boolean valid = miniAppUserService.validateToken(token);

        if (!valid) {
            return Result.fail("Token无效");
        }

        // 从token中提取手机号，然后查询用户信息
        try {
            String phone = com.dz.ms.util.JwtUtil.extractUsername(token);
            com.dz.ms.entity.MiniAppUser user = miniAppUserService.getByPhone(phone);
            if (user == null) {
                return Result.fail("用户不存在");
            }

            MiniAppUserVO userVO = miniAppUserService.getUserInfo(user.getId());
            return Result.success(userVO);
        } catch (Exception e) {
            logger.error("获取用户信息失败", e);
            return Result.fail("获取用户信息失败");
        }
    }

    @ApiOperation("更新用户信息")
    @PutMapping("/userInfo")
    public Result<String> updateUserInfo(@RequestHeader("Authorization") String authorization,
                                        @Valid @Validated({MiniAppUserDTO.Update.class, Default.class}) @RequestBody MiniAppUserDTO dto) {
        logger.info("更新用户信息请求");

        if (authorization == null || !authorization.startsWith("Bearer ")) {
            return Result.fail("Token格式错误");
        }

        String token = authorization.substring(7);
        boolean valid = miniAppUserService.validateToken(token);

        if (!valid) {
            return Result.fail("Token无效");
        }

        try {
            String phone = com.dz.ms.util.JwtUtil.extractUsername(token);
            com.dz.ms.entity.MiniAppUser user = miniAppUserService.getByPhone(phone);
            if (user == null) {
                return Result.fail("用户不存在");
            }

            boolean success = miniAppUserService.updateUserInfo(user.getId(), dto.getNickname(), dto.getAvatar());

            if (success) {
                logger.info("用户信息更新成功，用户ID: {}", user.getId());
                return Result.success("更新成功");
            } else {
                logger.warn("用户信息更新失败，用户ID: {}", user.getId());
                return Result.fail("更新失败");
            }
        } catch (Exception e) {
            logger.error("更新用户信息失败", e);
            return Result.fail("更新用户信息失败");
        }
    }
} 