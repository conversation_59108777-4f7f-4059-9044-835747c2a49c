package com.dz.ms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.ms.entity.Product;
import com.dz.ms.vo.ProductVO;

import java.util.List;

/**
 * 产品Service接口
 * <AUTHOR>
 * @date 2025-01-27
 * @version 1.0.0
 */
public interface ProductService extends IService<Product> {

    /**
     * 获取产品列表
     * @return 产品列表
     */
    List<ProductVO> getProductList();

    /**
     * 获取产品详情
     * @param id 产品ID
     * @return 产品详情
     */
    ProductVO getProductDetail(Long id);
} 