package com.dz.ms.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户案例实体
 * <AUTHOR>
 * @date 2025-01-27
 * @version 1.0.0
 */
@Data
@TableName("client")
public class Client implements Serializable {
    /** 主键ID */
    @TableId
    private Long id;

    /** 客户名称 */
    @TableField("name")
    private String name;

    /** 客户Logo */
    @TableField("logo")
    private String logo;

    /** 客户描述 */
    @TableField("description")
    private String description;

    /** 排序 */
    @TableField("sort_order")
    private Integer sortOrder;

    /** 状态 0-禁用 1-启用 */
    @TableField("status")
    private Integer status;

    /** 创建人 */
    @TableField("created_by")
    private Long createdBy;

    /** 修改人 */
    @TableField("updated_by")
    private Long updatedBy;

    /** 创建时间 */
    @TableField("created_at")
    private Date createdAt;

    /** 更新时间 */
    @TableField("updated_at")
    private Date updatedAt;
} 