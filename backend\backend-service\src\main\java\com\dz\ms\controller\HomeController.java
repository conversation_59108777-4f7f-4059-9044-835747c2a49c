package com.dz.ms.controller;

import com.dz.ms.common.Result;
import com.dz.ms.service.HomeService;
import com.dz.ms.vo.HomeDataVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 首页数据控制器
 * <AUTHOR>
 * @date 2025-01-27
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/home")
@Api(tags = "首页数据")
public class HomeController {

    private static final Logger logger = LoggerFactory.getLogger(HomeController.class);

    @Autowired
    private HomeService homeService;

    @ApiOperation("获取首页数据")
    @GetMapping
    public Result<HomeDataVO> getHomeData() {
        logger.info("获取首页数据请求");
        
        try {
            HomeDataVO homeData = homeService.getHomeData();
            return Result.success(homeData);
        } catch (Exception e) {
            logger.error("获取首页数据失败", e);
            return Result.fail("获取首页数据失败");
        }
    }
} 