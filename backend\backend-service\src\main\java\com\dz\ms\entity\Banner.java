package com.dz.ms.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 首页轮播图实体
 * <AUTHOR>
 * @date 2025-01-27
 * @version 1.0.0
 */
@Data
@TableName("banner")
public class Banner implements Serializable {
    /** 主键ID */
    @TableId
    private Long id;

    /** 图片URL */
    @TableField("image_url")
    private String imageUrl;

    /** 标题 */
    @TableField("title")
    private String title;

    /** 副标题 */
    @TableField("subtitle")
    private String subtitle;

    /** 链接地址 */
    @TableField("link_url")
    private String linkUrl;

    /** 排序 */
    @TableField("sort_order")
    private Integer sortOrder;

    /** 状态 0-禁用 1-启用 */
    @TableField("status")
    private Integer status;

    /** 创建人 */
    @TableField("created_by")
    private Long createdBy;

    /** 修改人 */
    @TableField("updated_by")
    private Long updatedBy;

    /** 创建时间 */
    @TableField("created_at")
    private Date createdAt;

    /** 更新时间 */
    @TableField("updated_at")
    private Date updatedAt;
} 