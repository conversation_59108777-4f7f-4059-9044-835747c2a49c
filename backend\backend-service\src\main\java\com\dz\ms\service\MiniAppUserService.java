package com.dz.ms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.ms.entity.MiniAppUser;
import com.dz.ms.vo.MiniAppUserVO;

/**
 * 小程序用户Service接口
 * <AUTHOR>
 * @date 2025-01-27
 * @version 1.0.0
 */
public interface MiniAppUserService extends IService<MiniAppUser> {

    /**
     * 用户注册
     * @param phone 手机号
     * @param password 密码
     * @param verificationCode 验证码
     * @param nickname 昵称
     * @param avatar 头像
     * @return 注册结果
     */
    boolean register(String phone, String password, String verificationCode, String nickname, String avatar);

    /**
     * 用户登录
     * @param phone 手机号
     * @param password 密码
     * @return 登录token
     */
    String login(String phone, String password);

    /**
     * 发送验证码
     * @param phone 手机号
     * @return 发送结果
     */
    boolean sendVerificationCode(String phone);

    /**
     * 验证token
     * @param token JWT token
     * @return 验证结果
     */
    boolean validateToken(String token);

    /**
     * 根据手机号查询用户
     * @param phone 手机号
     * @return 用户实体
     */
    MiniAppUser getByPhone(String phone);

    /**
     * 更新用户信息
     * @param userId 用户ID
     * @param nickname 昵称
     * @param avatar 头像
     * @return 更新结果
     */
    boolean updateUserInfo(Long userId, String nickname, String avatar);

    /**
     * 获取用户信息
     * @param userId 用户ID
     * @return 用户VO
     */
    MiniAppUserVO getUserInfo(Long userId);
} 