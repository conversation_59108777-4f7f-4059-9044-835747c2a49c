package com.dz.ms.controller;

import com.dz.ms.common.Result;
import com.dz.ms.service.ProductService;
import com.dz.ms.vo.ProductVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 产品控制器
 * <AUTHOR>
 * @date 2025-01-27
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/products")
@Api(tags = "产品管理")
public class ProductController {

    private static final Logger logger = LoggerFactory.getLogger(ProductController.class);

    @Autowired
    private ProductService productService;

    @ApiOperation("获取产品列表")
    @GetMapping
    public Result<List<ProductVO>> getProductList() {
        logger.info("获取产品列表请求");
        
        try {
            List<ProductVO> products = productService.getProductList();
            return Result.success(products);
        } catch (Exception e) {
            logger.error("获取产品列表失败", e);
            return Result.fail("获取产品列表失败");
        }
    }

    @ApiOperation("获取产品详情")
    @GetMapping("/{id}")
    public Result<ProductVO> getProductDetail(@PathVariable Long id) {
        logger.info("获取产品详情请求，产品ID: {}", id);
        
        try {
            ProductVO product = productService.getProductDetail(id);
            if (product == null) {
                return Result.fail("产品不存在");
            }
            return Result.success(product);
        } catch (Exception e) {
            logger.error("获取产品详情失败，产品ID: {}", id, e);
            return Result.fail("获取产品详情失败");
        }
    }
} 